<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BK_Database - النسخ الاحتياطي لقواعد البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2980b9;
            --success-color: #2ecc71;
            --danger-color: #e74c3c;
            --warning-color: #f39c12;
        }
        
        body {
            font-family: 'Tahoma', Arial, sans-serif;
            background-color: #f5f7fa;
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--secondary-color);
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 350px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- إشعارات -->
                <div id="notificationArea" class="notification"></div>
                
                <!-- بطاقة المشروع الرئيسية -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-database me-2"></i> BK_Database - النسخ الاحتياطي
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- قسم اختيار السيرفر -->
                        <div class="mb-4">
                            <h5 class="mb-3 border-bottom pb-2">
                                <i class="fas fa-server me-2"></i>1. تحديد السيرفر
                            </h5>
                            <div class="form-group">
                                <label for="serverSelect" class="form-label">اختر السيرفر:</label>
                                <select class="form-select" id="serverSelect">
                                    <option value="" selected disabled>-- اختر سيرفر --</option>
                                    <option value="server1">السيرفر الرئيسي (192.168.1.1)</option>
                                    <option value="server2">سيرفر النسخ الاحتياطي (***********)</option>
                                    <option value="server3">سيرفر التطوير (***********)</option>
                                    <option value="server4">سيرفر الاختبارات (192.168.1.4)</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- قسم إعدادات النسخ الاحتياطي -->
                        <div class="mb-4">
                            <h5 class="mb-3 border-bottom pb-2">
                                <i class="fas fa-cog me-2"></i>2. إعدادات النسخ الاحتياطي
                            </h5>
                            <div class="form-group mb-3">
                                <label for="backupName" class="form-label">اسم مجلد النسخة الاحتياطية:</label>
                                <div class="input-group">
                                    <span class="input-group-text">backup_</span>
                                    <input type="text" class="form-control" id="backupName" placeholder="اسم الملف" value="<?php echo date('Y-m-d'); ?>">
                                </div>
                                <small class="text-muted">يمكنك تغيير اسم المجلد كما تريد</small>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">قواعد البيانات المتاحة:</label>
                                <div class="border p-3 rounded bg-light">
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="db1" checked>
                                        <label class="form-check-label" for="db1">قاعدة البيانات الرئيسية</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="db2" checked>
                                        <label class="form-check-label" for="db2">قاعدة بيانات المستخدمين</label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="db3">
                                        <label class="form-check-label" for="db3">قاعدة بيانات المنتجات</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="db4">
                                        <label class="form-check-label" for="db4">قاعدة بيانات التسجيلات</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- أزرار التنفيذ -->
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-secondary" id="testConnection">
                                <i class="fas fa-plug me-2"></i>اختبار الاتصال
                            </button>
                            <button type="button" class="btn btn-primary" id="startBackup">
                                <i class="fas fa-play-circle me-2"></i>بدء النسخ الاحتياطي
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- سجل العمليات -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>السيرفر</th>
                                        <th>الحالة</th>
                                        <th>التفاصيل</th>
                                    </tr>
                                </thead>
                                <tbody id="operationsLog">
                                    <!-- سيتم ملؤها بالبيانات من JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة لعرض الإشعارات
        function showNotification(message, type = 'info') {
            const notificationArea = document.getElementById('notificationArea');
            const alertClasses = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            };
            
            const iconClasses = {
                'success': 'fa-check-circle',
                'error': 'fa-times-circle',
                'warning': 'fa-exclamation-triangle',
                'info': 'fa-info-circle'
            };
            
            const notification = document.createElement('div');
            notification.className = `alert ${alertClasses[type]} alert-dismissible fade show`;
            notification.innerHTML = `
                <i class="fas ${iconClasses[type]} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            notificationArea.appendChild(notification);
            
            // إزالة الإشعار بعد 5 ثواني
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
        
        // دالة لإضافة سجل جديد
        function addLogEntry(date, server, status, details) {
            const logTable = document.getElementById('operationsLog');
            const statusClass = status === 'نجاح' ? 'text-success' : status === 'تحذير' ? 'text-warning' : 'text-danger';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${date}</td>
                <td>${server}</td>
                <td class="${statusClass}">${status}</td>
                <td>${details}</td>
            `;
            
            logTable.insertBefore(row, logTable.firstChild);
        }
        
        // اختبار الاتصال
        document.getElementById('testConnection').addEventListener('click', function() {
            const serverSelect = document.getElementById('serverSelect');
            const selectedServer = serverSelect.options[serverSelect.selectedIndex].text;
            
            if (!selectedServer) {
                showNotification('الرجاء اختيار سيرفر أولاً', 'error');
                return;
            }
            
            // محاكاة اختبار الاتصال
            setTimeout(() => {
                showNotification(`تم اختبار الاتصال بنجاح مع ${selectedServer}`, 'success');
                addLogEntry(
                    new Date().toLocaleString(),
                    selectedServer,
                    'نجاح',
                    'اختبار اتصال ناجح'
                );
            }, 1500);
        });
        
        // بدء النسخ الاحتياطي
        document.getElementById('startBackup').addEventListener('click', function() {
            const serverSelect = document.getElementById('serverSelect');
            const selectedServer = serverSelect.options[serverSelect.selectedIndex].text;
            const backupName = document.getElementById('backupName').value;
            
            if (!selectedServer) {
                showNotification('الرجاء اختيار سيرفر أولاً', 'error');
                return;
            }
            
            if (!backupName) {
                showNotification('الرجاء إدخال اسم للمجلد الاحتياطي', 'error');
                return;
            }
            
            // محاكاة عملية النسخ الاحتياطي
            showNotification('جاري بدء عملية النسخ الاحتياطي...', 'info');
            
            setTimeout(() => {
                // 80% فرصة نجاح، 20% فشل لمحاكاة الواقع
                const isSuccess = Math.random() > 0.2;
                
                if (isSuccess) {
                    showNotification(`تم إنشاء النسخة الاحتياطية بنجاح في مجلد backup_${backupName}`, 'success');
                    addLogEntry(
                        new Date().toLocaleString(),
                        selectedServer,
                        'نجاح',
                        `تم النسخ الاحتياطي إلى backup_${backupName}`
                    );
                } else {
                    showNotification('فشل في إنشاء النسخة الاحتياطية!', 'error');
                    addLogEntry(
                        new Date().toLocaleString(),
                        selectedServer,
                        'فشل',
                        'خطأ في إنشاء النسخة الاحتياطية - تخزين غير كافي'
                    );
                }
            }, 3000);
        });
        
        // إضافة بعض السجلات المبدئية
        window.addEventListener('DOMContentLoaded', function() {
            addLogEntry(
                '2023-05-15 10:30:22',
                'السيرفر الرئيسي (192.168.1.1)',
                'نجاح',
                'تم النسخ الاحتياطي إلى backup_2023-05-15'
            );
            
            addLogEntry(
                '2023-05-14 22:15:10',
                'سيرفر النسخ الاحتياطي (***********)',
                'تحذير',
                'تم النسخ الاحتياطي مع بعض التحذيرات'
            );
            
            addLogEntry(
                '2023-05-13 08:45:37',
                'سيرفر التطوير (***********)',
                'فشل',
                'تعذر الاتصال بالسيرفر'
            );
        });
    </script>
</body>
</html>