<?php
try {
    // 1. تحديد مسار المجلد المشترك على السيرفر البعيد (UNC Path)
    $baseBackupPath = "\\192.168.106.200\home\BKs"; // مثال: \\192.168.1.100\Backups
    $folderName = "\Test_Backup_" . date("Ymd");
    $backupFolder = $baseBackupPath . $folderName;

    // إنشاء المجلد على السيرفر البعيد
    if (!is_dir($backupFolder)) {
        // التأكد من أن PHP لديه صلاحيات الكتابة على السيرفر البعيد
        if (!mkdir($backupFolder, 0777, true)) {
            throw new Exception("فشل إنشاء المجلد على السيرفر البعيد");
        }
        echo "تم إنشاء المجلد على السيرفر البعيد: " . $backupFolder . "\n";
    }

    // 2. تكوين مسار ملف النسخة الاحتياطية
    $databaseName = "YourDatabaseName";
    $backupFileName = $databaseName . "_" . date("Ymd") . ".bak";
    $backupPath = $backupFolder . "\\" . $backupFileName; // استخدام شرطة مائلة عكسية في UNC

    // 3. الاتصال بـ SQL Server
    $serverName = "YourSQLServerName"; // اسم سيرفر قاعدة البيانات
    $connectionOptions = array(
        "Database" => "master",
        "Uid" => "yourUsername",
        "PWD" => "yourPassword"
    );

    $conn = sqlsrv_connect($serverName, $connectionOptions);
    if (!$conn) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . print_r(sqlsrv_errors(), true));
    }

    // 4. تنفيذ أمر النسخ الاحتياطي مع تحديد مسار UNC
    $sqlCommand = "BACKUP DATABASE [$databaseName] 
                   TO DISK = '{$backupPath}' 
                   WITH COMPRESSION, INIT;";

    $stmt = sqlsrv_query($conn, $sqlCommand);
    if ($stmt === false) {
        throw new Exception("فشل النسخ الاحتياطي: " . print_r(sqlsrv_errors(), true));
    }

    echo "تم إنشاء النسخة الاحتياطية على السيرفر البعيد: " . $backupPath . "\n";

    // 5. ضغط الملف الاحتياطي (اختياري)
    $zipFileName = $databaseName . "_" . date("Ymd") . ".zip";
    $zipPath = $backupFolder . "\\" . $zipFileName;

    $zip = new ZipArchive();
    if ($zip->open($zipPath, ZipArchive::CREATE) === TRUE) {
        $zip->addFile($backupPath, basename($backupPath));
        $zip->close();
        unlink($backupPath); // حذف الملف الأصلي بعد الضغط
        echo "تم ضغط النسخة الاحتياطية إلى: " . $zipPath . "\n";
    } else {
        throw new Exception("فشل إنشاء ملف ZIP");
    }

    sqlsrv_close($conn);
} catch (Exception $e) {
    echo "حدث خطأ: " . $e->getMessage();
}
?>